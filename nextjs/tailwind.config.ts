import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Brand colors
        pink: {
          DEFAULT: '#ff2ebd',
          light: '#ff7ad6',
        },
        // Background colors
        black: '#000000',
        // Card colors
        'card-bg': '#111111',
        'card-border': '#333333',
        // Text colors
        'fg-primary': '#ffffff',
        'fg-secondary': '#cccccc',
      },
      fontFamily: {
        monoton: ['var(--font-monoton)', 'cursive'],
        sans: ['var(--font-sans)', 'system-ui', '-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'Roboto', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      zIndex: {
        '1030': '1030',
      },
      backdropBlur: {
        '20': '20px',
      },
    },
  },
  plugins: [],
}

export default config
