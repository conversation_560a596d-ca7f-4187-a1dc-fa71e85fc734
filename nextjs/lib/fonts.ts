/**
 * Font configuration for Chocolate & Art Show
 * Using Next.js font optimization for performance
 */

import { Monoton, Inter } from 'next/font/google';

// Monoton font for hero sections and neon effects
export const monoton = Monoton({
  weight: '400', // Monoton only comes in 400 weight
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-monoton',
});

// Inter font for body text and UI elements
export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-sans',
});

// Export font class names for use in components
export const fontClasses = {
  monoton: monoton.className,
  monotonVariable: monoton.variable,
  inter: inter.className,
  interVariable: inter.variable,
};
